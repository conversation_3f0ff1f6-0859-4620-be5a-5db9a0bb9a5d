use serenity::all::ComponentInteractionDataKind;
use serenity::all::CreateInteractionResponse;
use serenity::all::CreateInteractionResponseMessage;
use serenity::all::CreateMessage;
use serenity::all::Interaction;
use serenity::all::InteractionResponseFlags;
use serenity::async_trait;
use serenity::prelude::*;
use tracing::error;
use tracing::info;

use crate::components::TICKET_CHANNEL_CREATED_MESSAGE;
use crate::components::TICKET_CHANNEL_EXISTS_MESSAGE;
use crate::components::TICKET_CREATE_BUTTON_ID;
use crate::components::UNEXPECTED_ERROR_MESSAGE;
use crate::components::support_description_embed;
use crate::state::SerenityState;

pub struct Handler;

#[async_trait]
impl EventHandler for Handler {
    async fn interaction_create(&self, ctx: Context, interaction: Interaction) {
        if let Interaction::Component(interaction) = &interaction {
            if let ComponentInteractionDataKind::Button = interaction.data.kind {
                if interaction.data.custom_id.as_str() != TICKET_CREATE_BUTTON_ID {
                    return;
                }

                info!(
                    "recv ticket creation request; user_id={}",
                    interaction.user.id.get()
                );

                let data = ctx.data.read().await;

                let state = match data.get::<SerenityState>() {
                    Some(state) => state,
                    None => {
                        error!(
                            "failed to get SerenityState from the data; user_id={}",
                            interaction.user.id.get()
                        );

                        if let Err(error) = interaction
                            .create_response(
                                &ctx.http,
                                CreateInteractionResponse::Message(
                                    CreateInteractionResponseMessage::new()
                                        .content(UNEXPECTED_ERROR_MESSAGE)
                                        .flags(InteractionResponseFlags::EPHEMERAL),
                                ),
                            )
                            .await
                        {
                            error!(
                                "failed to create interaction response to ticket channel; user_id={} error={:#?}",
                                interaction.user.id, error
                            );
                        }

                        return;
                    }
                };

                match state.tickets.upsert_ticket(&interaction.user.id).await {
                    Ok(result) => match result {
                        (ticket, true) => {
                            if let Err(error) = ticket
                                .channel
                                .send_message(
                                    &ctx.http,
                                    CreateMessage::new().embed(support_description_embed()),
                                )
                                .await
                            {
                                error!(
                                    "failed to send support description embed to ticket channel; user_id={} error={:#?}",
                                    interaction.user.id, error
                                );
                            }

                            if let Err(error) = interaction
                                .create_response(
                                    &ctx.http,
                                    CreateInteractionResponse::Message(
                                        CreateInteractionResponseMessage::new()
                                            .content(TICKET_CHANNEL_CREATED_MESSAGE)
                                            .flags(InteractionResponseFlags::EPHEMERAL),
                                    ),
                                )
                                .await
                            {
                                error!(
                                    "failed to create interaction response to ticket channel; user_id={} error={:#?}",
                                    interaction.user.id, error
                                );
                            }

                            info!(
                                "fulfilled ticket creation request created; user_id={} channel_id={} ticket={}",
                                interaction.user.id, ticket.channel.id, ticket.value
                            );
                        }
                        (ticket, false) => {
                            if let Err(error) = interaction
                                .create_response(
                                    &ctx.http,
                                    CreateInteractionResponse::Message(
                                        CreateInteractionResponseMessage::new()
                                            .content(TICKET_CHANNEL_EXISTS_MESSAGE)
                                            .flags(InteractionResponseFlags::EPHEMERAL),
                                    ),
                                )
                                .await
                            {
                                error!(
                                    "failed to create interaction response to ticket channel; user_id={} error={:#?}",
                                    interaction.user.id, error
                                );
                            }

                            info!(
                                "fulfilled ticket creation request (already exists); user_id={} channel_id={} ticket={}",
                                interaction.user.id, ticket.channel.id, ticket.value
                            );
                        }
                    },
                    Err(error) => {
                        error!(
                            "failed to upsert ticket; user_id={} error={:#?}",
                            interaction.user.id, error
                        );

                        if let Err(error) = interaction
                            .create_response(
                                &ctx.http,
                                CreateInteractionResponse::Message(
                                    CreateInteractionResponseMessage::new()
                                        .content(UNEXPECTED_ERROR_MESSAGE)
                                        .flags(InteractionResponseFlags::EPHEMERAL),
                                ),
                            )
                            .await
                        {
                            error!(
                                "failed to create interaction response to ticket channel; user_id={} error={:#?}",
                                interaction.user.id, error
                            );
                        }

                        return;
                    }
                };
            }
        }
    }
}
