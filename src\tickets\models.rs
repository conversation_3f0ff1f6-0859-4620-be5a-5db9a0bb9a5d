use std::fmt::Display;

use serde::{Deserialize, Serialize};
use serenity::{
    all::GuildId,
    model::id::{ChannelId, UserId},
};

use crate::trengo::models::{ContactId, ContractName};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize)]
pub struct Mailbox {
    pub guild_id: GuildId,
    pub category_id: ChannelId,
    pub channel_id: ChannelId,
}

impl Display for Mailbox {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "mailbox|guild_id={}|category_id={}|channel_id={}",
            self.guild_id, self.category_id, self.channel_id
        )
    }
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Serialize, Deserialize)]
pub struct Ticket {
    pub user_id: UserId,
    pub channel_id: ChannelId,
    pub contact_id: ContactId,
    pub contact_name: ContractName,
}

impl Display for Ticket {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "ticket|user_id={}|channel_id={}|contact_id={}|contact_name={}",
            self.user_id, self.channel_id, self.contact_id, self.contact_name
        )
    }
}
