use rocket::{State, futures::future::join_all, post, serde::json::<PERSON><PERSON>};
use serde::Deserialize;
use serenity::all::{CreateAttachment, CreateMessage, UserId};
use tracing::{debug, error, info};

use crate::{
    state::RocketState,
    trengo::{self, models::ContactId},
};

#[derive(Debug, Deserialize)]
pub struct WebhookPayload<'a> {
    message_id: &'a str,
    ticket_id: &'a str,
    contact_identifier: Option<&'a str>,
}

#[post("/webhook", format = "json", data = "<payload>")]
pub async fn handler<'a>(payload: Json<WebhookPayload<'a>>, state: &State<RocketState>) {
    // TODO: verify signature of payload

    if payload.contact_identifier.is_none() {
        return;
    }

    let user_id = {
        let contact_id = ContactId::new(payload.contact_identifier.unwrap());

        if contact_id.is_none() {
            debug!(
                "recv request for non-custom channel; path=/webhook payload={:#?}",
                payload,
            );

            return;
        }

        UserId::from(contact_id.unwrap())
    };

    info!(
        "recv request to store message in discord; user_id={}",
        user_id
    );

    let (record, _has_created_channel) = match state.tickets.upsert_ticket(&user_id).await {
        Ok(record) => record,
        Err(error) => {
            error!(
                "failed to upsert ticket; user_id={} error={:#?}",
                user_id, error
            );

            return;
        }
    };

    match trengo::fetch_message(payload.ticket_id, payload.message_id).await {
        Ok(message) => {
            let attachments = join_all(
                message
                    .attachment_urls
                    .into_iter()
                    .map(async |url| CreateAttachment::url(&state.discord_http, &url).await),
            )
            .await
            .into_iter()
            .filter(|attachment| attachment.is_ok())
            .map(|attachment| attachment.unwrap())
            .collect::<Vec<_>>();

            let builder = CreateMessage::new()
                .content(message.content)
                .add_files(attachments);

            match record
                .channel
                .send_message(&state.discord_http, builder)
                .await
            {
                Ok(message) => {
                    info!(
                        "stored message in discord; user_id={} message={:#?}",
                        record.value.user_id, message
                    );
                }
                Err(why) => {
                    error!(
                        "failed to send message to discord; user_id={} error={:#?}",
                        record.value.user_id, why
                    );
                }
            }
        }
        Err(error) => {
            error!(
                "failed to fetch message from trengo; user_id={} error={:#?}",
                record.value.user_id, error,
            );
        }
    }
}
